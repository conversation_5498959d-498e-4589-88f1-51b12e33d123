// Products Page JavaScript
// Enhanced product catalog with filtering, search, and quick view

let allProducts = [];
let filteredProducts = [];
let currentCategory = "all";
let currentSort = "name";
let currentPage = 1;
let productsPerPage = 12;
let quickViewQuantity = 1;
let currentQuickViewProduct = null;

// Extended product catalog with categories
const extendedProducts = [
  // Peripherals
  {
    id: 1,
    name: "Neon Cube Gaming Mouse",
    description:
      "Precision gaming mouse with RGB lighting and geometric design",
    price: 79.99,
    icon: "🖱️",
    category: "peripherals",
    features: [
      "12000 DPI",
      "RGB Lighting",
      "Ergonomic Design",
      "Programmable Buttons",
    ],
  },
  {
    id: 2,
    name: "Skeletal Mechanical Keyboard",
    description:
      "Mechanical keyboard with bone-white keycaps and cyan backlighting",
    price: 149.99,
    icon: "⌨️",
    category: "peripherals",
    features: [
      "Cherry MX Switches",
      "RGB Backlighting",
      "Anti-Ghosting",
      "USB-C Connection",
    ],
  },
  {
    id: 3,
    name: "Geometry Dash Headset",
    description: "Premium gaming headset with spatial audio and neon accents",
    price: 199.99,
    icon: "🎧",
    category: "peripherals",
    features: [
      "7.1 Surround Sound",
      "Noise Cancellation",
      "Retractable Mic",
      "50mm Drivers",
    ],
  },
  {
    id: 9,
    name: "Neon Gaming Controller",
    description:
      "Wireless controller with customizable RGB and haptic feedback",
    price: 89.99,
    icon: "🎮",
    category: "peripherals",
    features: [
      "Wireless Connectivity",
      "Haptic Feedback",
      "Customizable RGB",
      "20-hour Battery",
    ],
  },
  {
    id: 10,
    name: "Skeletal Webcam",
    description: "4K streaming webcam with auto-focus and RGB ring light",
    price: 129.99,
    icon: "📹",
    category: "peripherals",
    features: [
      "4K Resolution",
      "Auto-Focus",
      "RGB Ring Light",
      "Privacy Shutter",
    ],
  },

  // Accessories
  {
    id: 4,
    name: "Triangular Gaming Pad",
    description: "Large gaming mousepad with geometric patterns",
    price: 39.99,
    icon: "🔺",
    category: "accessories",
    features: [
      "XXL Size",
      "Non-slip Base",
      "Water Resistant",
      "Stitched Edges",
    ],
  },
  {
    id: 6,
    name: "Cube Monitor Stand",
    description: "Adjustable monitor stand with geometric cube design",
    price: 89.99,
    icon: "📺",
    category: "accessories",
    features: [
      "Height Adjustable",
      "Cable Management",
      "Sturdy Build",
      "Modern Design",
    ],
  },
  {
    id: 7,
    name: "Skeletal Phone Case",
    description: "Protective phone case with glow-in-the-dark skeletal pattern",
    price: 24.99,
    icon: "📱",
    category: "accessories",
    features: [
      "Drop Protection",
      "Glow-in-Dark",
      "Wireless Charging",
      "Precise Cutouts",
    ],
  },
  {
    id: 11,
    name: "Neon Cable Management Kit",
    description: "RGB cable management system with geometric clips",
    price: 34.99,
    icon: "🔌",
    category: "accessories",
    features: [
      "RGB Lighting",
      "Magnetic Clips",
      "Cable Sleeves",
      "Remote Control",
    ],
  },
  {
    id: 12,
    name: "Geometric Desk Organizer",
    description: "Multi-compartment desk organizer with neon accents",
    price: 49.99,
    icon: "📦",
    category: "accessories",
    features: [
      "Multiple Compartments",
      "LED Accents",
      "Wireless Charging Pad",
      "Premium Materials",
    ],
  },

  // Apparel
  {
    id: 8,
    name: "Geometry Dash T-Shirt",
    description: "Premium cotton t-shirt with Skeletal Shenanigans design",
    price: 29.99,
    icon: "👕",
    category: "apparel",
    features: [
      "100% Cotton",
      "Glow-in-Dark Print",
      "Comfortable Fit",
      "Machine Washable",
    ],
  },
  {
    id: 13,
    name: "Neon Gaming Hoodie",
    description: "Comfortable hoodie with embroidered geometric patterns",
    price: 59.99,
    icon: "🧥",
    category: "apparel",
    features: [
      "Soft Fleece",
      "Embroidered Design",
      "Kangaroo Pocket",
      "Adjustable Hood",
    ],
  },
  {
    id: 14,
    name: "Skeletal Gaming Cap",
    description: "Snapback cap with 3D embroidered skull logo",
    price: 24.99,
    icon: "🧢",
    category: "apparel",
    features: [
      "Adjustable Fit",
      "3D Embroidery",
      "Breathable Fabric",
      "Curved Brim",
    ],
  },
  {
    id: 15,
    name: "Geometry Socks Pack",
    description: "Set of 3 gaming socks with geometric patterns",
    price: 19.99,
    icon: "🧦",
    category: "apparel",
    features: [
      "3-Pack Set",
      "Moisture Wicking",
      "Cushioned Sole",
      "Geometric Patterns",
    ],
  },

  // Furniture
  {
    id: 5,
    name: "Neon Gaming Chair",
    description: "Ergonomic gaming chair with LED strips and skeletal design",
    price: 399.99,
    icon: "🪑",
    category: "furniture",
    features: [
      "Ergonomic Design",
      "LED Lighting",
      "Lumbar Support",
      "360° Swivel",
    ],
  },
  {
    id: 16,
    name: "Geometric Gaming Desk",
    description: "L-shaped gaming desk with RGB lighting and cable management",
    price: 299.99,
    icon: "🪑",
    category: "furniture",
    features: [
      "L-Shaped Design",
      "RGB Lighting",
      "Cable Management",
      "Carbon Fiber Surface",
    ],
  },
  {
    id: 17,
    name: "Skeletal Bookshelf",
    description: "Modern bookshelf with geometric compartments and LED strips",
    price: 199.99,
    icon: "📚",
    category: "furniture",
    features: [
      "Geometric Design",
      "LED Strips",
      "Multiple Shelves",
      "Easy Assembly",
    ],
  },
  {
    id: 18,
    name: "Neon Floor Lamp",
    description: "RGB floor lamp with geometric shade and app control",
    price: 149.99,
    icon: "💡",
    category: "furniture",
    features: [
      "RGB Colors",
      "App Control",
      "Geometric Shade",
      "Touch Controls",
    ],
  },
];

// Initialize products page
document.addEventListener("DOMContentLoaded", function () {
  allProducts = [...extendedProducts];
  filteredProducts = [...allProducts];

  setupEventListeners();
  displayProducts();
  updateResultsCount();
  initializeAnimations();
});

// Setup event listeners
function setupEventListeners() {
  // Search functionality
  const searchInput = document.getElementById("searchInput");
  searchInput.addEventListener("input", debounce(handleSearch, 300));

  // Filter buttons
  const filterBtns = document.querySelectorAll(".filter-btn");
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      filterBtns.forEach((b) => b.classList.remove("active"));
      this.classList.add("active");
      currentCategory = this.dataset.category;
      currentPage = 1;
      applyFilters();
    });
  });

  // Sort dropdown
  const sortSelect = document.getElementById("sortSelect");
  sortSelect.addEventListener("change", function () {
    currentSort = this.value;
    applyFilters();
  });

  // Load more button
  const loadMoreBtn = document.getElementById("loadMoreBtn");
  loadMoreBtn.addEventListener("click", loadMoreProducts);
}

// Handle search input
function handleSearch() {
  const searchTerm = document.getElementById("searchInput").value.toLowerCase();
  currentPage = 1;
  applyFilters(searchTerm);
}

// Apply filters and sorting
function applyFilters(searchTerm = "") {
  let filtered = [...allProducts];

  // Apply category filter
  if (currentCategory !== "all") {
    filtered = filtered.filter(
      (product) => product.category === currentCategory
    );
  }

  // Apply search filter
  if (searchTerm) {
    filtered = filtered.filter(
      (product) =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm)
    );
  }

  // Apply sorting
  filtered.sort((a, b) => {
    switch (currentSort) {
      case "name":
        return a.name.localeCompare(b.name);
      case "price-low":
        return a.price - b.price;
      case "price-high":
        return b.price - a.price;
      case "newest":
        return b.id - a.id;
      default:
        return 0;
    }
  });

  filteredProducts = filtered;
  displayProducts();
  updateResultsCount();
}

// Display products
function displayProducts() {
  const productsGrid = document.getElementById("productsGrid");
  const startIndex = 0;
  const endIndex = currentPage * productsPerPage;
  const productsToShow = filteredProducts.slice(startIndex, endIndex);

  productsGrid.innerHTML = "";

  productsToShow.forEach((product, index) => {
    const productCard = createEnhancedProductCard(product, index);
    productsGrid.appendChild(productCard);
  });

  // Update load more button
  const loadMoreBtn = document.getElementById("loadMoreBtn");
  if (endIndex >= filteredProducts.length) {
    loadMoreBtn.style.display = "none";
  } else {
    loadMoreBtn.style.display = "inline-flex";
  }
}

// Create enhanced product card
function createEnhancedProductCard(product, index) {
  const card = document.createElement("div");
  card.className = "product-card";
  card.style.animationDelay = `${index * 0.1}s`;

  card.innerHTML = `
        <div class="product-category">${product.category}</div>
        <button class="quick-view-btn" onclick="openQuickView(${product.id})">
            <i class="fas fa-eye"></i>
        </button>
        <div class="product-image">
            ${product.icon}
        </div>
        <h3 class="product-title">${product.name}</h3>
        <p class="product-description">${product.description}</p>
        <div class="product-price">$${product.price.toFixed(2)}</div>
        <button class="add-to-cart" onclick="addToCart(${product.id})">
            Add to Cart
        </button>
    `;

  return card;
}

// Load more products
function loadMoreProducts() {
  currentPage++;
  displayProducts();
}

// Update results count
function updateResultsCount() {
  const resultsCount = document.getElementById("resultsCount");
  resultsCount.textContent = filteredProducts.length;
}

// Open quick view modal
function openQuickView(productId) {
  const product = allProducts.find((p) => p.id === productId);
  if (!product) return;

  currentQuickViewProduct = product;
  quickViewQuantity = 1;

  // Populate quick view content
  document.getElementById("quickViewImage").innerHTML = product.icon;
  document.getElementById("quickViewTitle").textContent = product.name;
  document.getElementById("quickViewDescription").textContent =
    product.description;
  document.getElementById(
    "quickViewPrice"
  ).textContent = `$${product.price.toFixed(2)}`;
  document.getElementById("quickViewQuantity").textContent = quickViewQuantity;

  // Populate features
  const featuresList = document.getElementById("quickViewFeatures");
  featuresList.innerHTML = "";
  product.features.forEach((feature) => {
    const li = document.createElement("li");
    li.textContent = feature;
    featuresList.appendChild(li);
  });

  // Show modal
  document.getElementById("quickViewModal").classList.add("active");
}

// Close quick view modal
function closeQuickView() {
  document.getElementById("quickViewModal").classList.remove("active");
  currentQuickViewProduct = null;
}

// Change quantity in quick view
function changeQuantity(change) {
  quickViewQuantity = Math.max(1, quickViewQuantity + change);
  document.getElementById("quickViewQuantity").textContent = quickViewQuantity;
}

// Add to cart from quick view
function addToCartFromQuickView() {
  if (!currentQuickViewProduct) return;

  for (let i = 0; i < quickViewQuantity; i++) {
    addToCart(currentQuickViewProduct.id);
  }

  closeQuickView();
}

// Filter products from footer links
function filterProducts(category) {
  currentCategory = category;
  currentPage = 1;

  // Update active filter button
  const filterBtns = document.querySelectorAll(".filter-btn");
  filterBtns.forEach((btn) => {
    btn.classList.remove("active");
    if (btn.dataset.category === category) {
      btn.classList.add("active");
    }
  });

  applyFilters();

  // Scroll to products
  document
    .querySelector(".products-main")
    .scrollIntoView({ behavior: "smooth" });
}

// Debounce function for search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Toggle mobile menu
function toggleMenu() {
  const navMenu = document.querySelector(".nav-menu");
  const hamburger = document.querySelector(".hamburger");
  navMenu.classList.toggle("active");
  hamburger.classList.toggle("active");
}

// Initialize animations
function initializeAnimations() {
  // Parallax effect for geometric shapes
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll(".shape");

    shapes.forEach((shape, index) => {
      const speed = 0.3 + index * 0.1;
      shape.style.transform = `translateY(${scrolled * speed}px) rotate(${
        scrolled * 0.05
      }deg)`;
    });
  });
}
