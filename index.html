<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Skeletal Shenanigans Store - Geometry Dash Inspired E-commerce
    </title>
    <link rel="stylesheet" href="styles.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <i class="fas fa-skull"></i>
          <span>SKELETAL STORE</span>
        </div>
        <ul class="nav-menu">
          <li><a href="index.html" class="nav-link active">HOME</a></li>
          <li><a href="products.html" class="nav-link">PRODUCTS</a></li>
          <li><a href="about.html" class="nav-link">ABOUT</a></li>
          <li><a href="contact.html" class="nav-link">CONTACT</a></li>
        </ul>
        <div class="nav-icons">
          <div class="cart-icon" onclick="toggleCart()">
            <i class="fas fa-shopping-cart"></i>
            <span class="cart-count">0</span>
          </div>
          <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-bg">
        <div class="geometric-shapes">
          <div class="shape triangle"></div>
          <div class="shape square"></div>
          <div class="shape diamond"></div>
          <div class="shape hexagon"></div>
        </div>
      </div>
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="glitch" data-text="SKELETAL">SKELETAL</span>
          <span class="glitch" data-text="SHENANIGANS">SHENANIGANS</span>
        </h1>
        <p class="hero-subtitle">Geometry Dash Inspired Gaming Gear</p>
        <button class="cta-button" onclick="scrollToProducts()">
          <span>EXPLORE STORE</span>
          <i class="fas fa-arrow-right"></i>
        </button>
      </div>
      <div class="scroll-indicator">
        <div class="scroll-arrow"></div>
      </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products-section">
      <div class="container">
        <h2 class="section-title">
          <span class="title-line"></span>
          FEATURED PRODUCTS
          <span class="title-line"></span>
        </h2>
        <div class="products-grid" id="productsGrid">
          <!-- Products will be dynamically loaded here -->
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
      <div class="container">
        <div class="about-content">
          <div class="about-text">
            <h2>ABOUT SKELETAL SHENANIGANS</h2>
            <p>
              Inspired by the legendary Geometry Dash level "Skeletal
              Shenanigans" by YoReid and Airz, our store brings you the most
              epic gaming gear with that distinctive geometric aesthetic.
            </p>
            <div class="stats">
              <div class="stat">
                <span class="stat-number">1000+</span>
                <span class="stat-label">Products</span>
              </div>
              <div class="stat">
                <span class="stat-number">50K+</span>
                <span class="stat-label">Customers</span>
              </div>
              <div class="stat">
                <span class="stat-number">99%</span>
                <span class="stat-label">Satisfaction</span>
              </div>
            </div>
          </div>
          <div class="about-visual">
            <div class="geometric-pattern"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Shopping Cart -->
    <div id="cartModal" class="cart-modal">
      <div class="cart-content">
        <div class="cart-header">
          <h3>SHOPPING CART</h3>
          <button class="close-cart" onclick="toggleCart()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="cart-items" id="cartItems">
          <!-- Cart items will be dynamically loaded here -->
        </div>
        <div class="cart-footer">
          <div class="cart-total">
            <span>Total: $<span id="cartTotal">0.00</span></span>
          </div>
          <button class="checkout-btn" onclick="checkout()">
            <span>CHECKOUT</span>
            <i class="fas fa-credit-card"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>SKELETAL STORE</h4>
            <p>
              Geometry Dash inspired gaming gear for the ultimate gaming
              experience.
            </p>
          </div>
          <div class="footer-section">
            <h4>QUICK LINKS</h4>
            <ul>
              <li><a href="index.html">Home</a></li>
              <li><a href="products.html">Products</a></li>
              <li><a href="about.html">About</a></li>
              <li><a href="contact.html">Contact</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>FOLLOW US</h4>
            <div class="social-links">
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
              <a href="#"><i class="fab fa-discord"></i></a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>
            &copy; 2024 Skeletal Shenanigans Store. Inspired by Geometry Dash.
          </p>
        </div>
      </div>
    </footer>

    <script src="script.js"></script>
  </body>
</html>
