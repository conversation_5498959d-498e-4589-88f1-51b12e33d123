/* Checkout Page Specific Styles */

/* Checkout Hero */
.checkout-hero {
    height: 40vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 80px;
}

.checkout-header {
    text-align: center;
    z-index: 2;
}

.checkout-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid var(--neon-cyan);
    border-radius: 6px;
    color: var(--neon-cyan);
    font-size: 0.9rem;
    font-weight: 600;
}

/* Checkout Section */
.checkout-section {
    padding: 3rem 0;
    background: var(--secondary-bg);
    min-height: 100vh;
}

.checkout-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Order Summary */
.order-summary {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--neon-cyan);
    text-shadow: 0 0 10px var(--neon-cyan);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.order-items {
    margin-bottom: 2rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-glow);
}

.order-item-image {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-bg);
}

.order-item-details {
    flex: 1;
}

.order-item-name {
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.order-item-quantity {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.order-item-price {
    color: var(--neon-green);
    font-weight: 700;
    text-shadow: 0 0 5px var(--neon-green);
}

.order-totals {
    border-top: 2px solid var(--border-glow);
    padding-top: 1rem;
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.final-total {
    font-size: 1.3rem;
    color: var(--neon-green);
    text-shadow: 0 0 10px var(--neon-green);
    border-top: 1px solid var(--border-glow);
    padding-top: 0.5rem;
    margin-top: 1rem;
}

/* Checkout Form */
.checkout-form {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 900;
    color: var(--neon-purple);
    text-shadow: 0 0 10px var(--neon-purple);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    background: var(--secondary-bg);
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--neon-cyan);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.input-glow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.form-group input:focus + .input-glow,
.form-group select:focus + .input-glow {
    width: 100%;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.payment-method {
    flex: 1;
    padding: 1rem;
    background: var(--secondary-bg);
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.payment-method:hover {
    border-color: var(--neon-cyan);
    background: rgba(0, 255, 255, 0.1);
}

.payment-method.active {
    border-color: var(--neon-cyan);
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.payment-method i {
    font-size: 1.5rem;
    color: var(--neon-cyan);
}

.card-icons {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 0.5rem;
}

.card-icons i {
    font-size: 1.5rem;
    color: var(--text-secondary);
}

/* Submit Button */
.complete-order-btn {
    width: 100%;
    background: var(--gradient-primary);
    border: none;
    padding: 1.5rem;
    font-size: 1.3rem;
    font-weight: 900;
    color: var(--primary-bg);
    cursor: pointer;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-family: 'Orbitron', monospace;
    margin-bottom: 1rem;
}

.complete-order-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.4);
}

.security-note {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.security-note i {
    color: var(--neon-green);
}

/* Success Modal */
.success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
}

.success-modal.active {
    display: flex;
}

.success-content {
    background: var(--secondary-bg);
    border: 2px solid var(--neon-green);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 255, 0, 0.3);
}

.success-animation {
    margin-bottom: 2rem;
}

.success-animation i {
    font-size: 4rem;
    color: var(--neon-green);
    animation: successPulse 2s infinite;
}

.success-content h2 {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: var(--neon-green);
    text-shadow: 0 0 20px var(--neon-green);
    margin-bottom: 1rem;
}

.success-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.order-number {
    background: var(--accent-bg);
    border: 1px solid var(--border-glow);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 2rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--neon-cyan);
}

.back-to-store-btn {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-bg);
    cursor: pointer;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-family: 'Orbitron', monospace;
}

.back-to-store-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .order-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .checkout-title {
        font-size: 2rem;
    }
    
    .security-badges {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .payment-methods {
        flex-direction: column;
    }
    
    .checkout-form,
    .order-summary {
        padding: 1.5rem;
    }
}
