<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Skeletal Shenanigans Store</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="products.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-skull"></i>
                <span>SKELETAL STORE</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">HOME</a></li>
                <li><a href="products.html" class="nav-link active">PRODUCTS</a></li>
                <li><a href="about.html" class="nav-link">ABOUT</a></li>
                <li><a href="contact.html" class="nav-link">CONTACT</a></li>
            </ul>
            <div class="nav-icons">
                <div class="cart-icon" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </div>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Products Hero -->
    <section class="products-hero">
        <div class="hero-bg">
            <div class="geometric-shapes">
                <div class="shape triangle"></div>
                <div class="shape square"></div>
                <div class="shape diamond"></div>
                <div class="shape hexagon"></div>
            </div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="glitch" data-text="GAMING">GAMING</span>
                <span class="glitch" data-text="ARSENAL">ARSENAL</span>
            </h1>
            <p class="hero-subtitle">Discover the Ultimate Gaming Gear Collection</p>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="Search products...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="filter-categories">
                    <button class="filter-btn active" data-category="all">ALL</button>
                    <button class="filter-btn" data-category="peripherals">PERIPHERALS</button>
                    <button class="filter-btn" data-category="accessories">ACCESSORIES</button>
                    <button class="filter-btn" data-category="apparel">APPAREL</button>
                    <button class="filter-btn" data-category="furniture">FURNITURE</button>
                </div>
                <div class="sort-options">
                    <select id="sortSelect">
                        <option value="name">Sort by Name</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="newest">Newest First</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="products-main">
        <div class="container">
            <div class="products-header">
                <h2 class="section-title">
                    <span class="title-line"></span>
                    PRODUCT CATALOG
                    <span class="title-line"></span>
                </h2>
                <div class="results-count">
                    <span id="resultsCount">0</span> products found
                </div>
            </div>
            <div class="products-grid" id="productsGrid">
                <!-- Products will be dynamically loaded here -->
            </div>
            <div class="load-more-container">
                <button class="load-more-btn" id="loadMoreBtn">
                    <span>LOAD MORE</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Product Quick View Modal -->
    <div id="quickViewModal" class="quick-view-modal">
        <div class="quick-view-content">
            <button class="close-quick-view" onclick="closeQuickView()">
                <i class="fas fa-times"></i>
            </button>
            <div class="quick-view-grid">
                <div class="quick-view-image">
                    <div class="product-image-large" id="quickViewImage">
                        <!-- Product icon will be displayed here -->
                    </div>
                </div>
                <div class="quick-view-details">
                    <h3 id="quickViewTitle"></h3>
                    <p id="quickViewDescription"></p>
                    <div class="quick-view-price" id="quickViewPrice"></div>
                    <div class="quick-view-features">
                        <h4>Features:</h4>
                        <ul id="quickViewFeatures"></ul>
                    </div>
                    <div class="quick-view-actions">
                        <div class="quantity-selector">
                            <button class="qty-btn" onclick="changeQuantity(-1)">-</button>
                            <span id="quickViewQuantity">1</span>
                            <button class="qty-btn" onclick="changeQuantity(1)">+</button>
                        </div>
                        <button class="add-to-cart-large" onclick="addToCartFromQuickView()">
                            <span>ADD TO CART</span>
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shopping Cart Modal -->
    <div id="cartModal" class="cart-modal">
        <div class="cart-content">
            <div class="cart-header">
                <h3>SHOPPING CART</h3>
                <button class="close-cart" onclick="toggleCart()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="cart-items" id="cartItems">
                <!-- Cart items will be dynamically loaded here -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <span>Total: $<span id="cartTotal">0.00</span></span>
                </div>
                <button class="checkout-btn" onclick="checkout()">
                    <span>CHECKOUT</span>
                    <i class="fas fa-credit-card"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>SKELETAL STORE</h4>
                    <p>Geometry Dash inspired gaming gear for the ultimate gaming experience.</p>
                </div>
                <div class="footer-section">
                    <h4>QUICK LINKS</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>CATEGORIES</h4>
                    <ul>
                        <li><a href="#" onclick="filterProducts('peripherals')">Peripherals</a></li>
                        <li><a href="#" onclick="filterProducts('accessories')">Accessories</a></li>
                        <li><a href="#" onclick="filterProducts('apparel')">Apparel</a></li>
                        <li><a href="#" onclick="filterProducts('furniture')">Furniture</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>FOLLOW US</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-discord"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Skeletal Shenanigans Store. Inspired by Geometry Dash.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="products.js"></script>
</body>
</html>
