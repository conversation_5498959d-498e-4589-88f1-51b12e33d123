/* About Page Specific Styles */

/* About Hero */
.about-hero {
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 80px;
}

/* Story Section */
.story-section {
    padding: 5rem 0;
    background: var(--secondary-bg);
}

.story-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--neon-cyan);
    text-shadow: 0 0 20px var(--neon-cyan);
    margin-bottom: 2rem;
}

.story-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.story-stats {
    display: flex;
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    border-color: var(--neon-purple);
    box-shadow: 0 10px 30px rgba(255, 0, 255, 0.2);
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: var(--neon-green);
    text-shadow: 0 0 15px var(--neon-green);
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    font-weight: 600;
}

.story-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.geometric-showcase {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    width: 300px;
    height: 300px;
}

.showcase-item {
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--primary-bg);
    animation: float 3s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.showcase-item:nth-child(1) { animation-delay: 0s; }
.showcase-item:nth-child(2) { animation-delay: 0.5s; }
.showcase-item:nth-child(3) { animation-delay: 1s; }
.showcase-item:nth-child(4) { animation-delay: 1.5s; }

/* Values Section */
.values-section {
    padding: 5rem 0;
    background: var(--primary-bg);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-card {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.value-card:hover::before {
    left: 100%;
}

.value-card:hover {
    border-color: var(--neon-cyan);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.2);
    transform: translateY(-10px);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--primary-bg);
}

.value-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Team Section */
.team-section {
    padding: 5rem 0;
    background: var(--secondary-bg);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.team-member {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.team-member:hover {
    border-color: var(--neon-purple);
    box-shadow: 0 15px 40px rgba(255, 0, 255, 0.2);
    transform: translateY(-5px);
}

.member-avatar {
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: var(--primary-bg);
    border: 3px solid var(--neon-cyan);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.team-member h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.member-role {
    color: var(--neon-green);
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-shadow: 0 0 10px var(--neon-green);
}

.member-bio {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.member-social {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.member-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--secondary-bg);
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.member-social a:hover {
    border-color: var(--neon-cyan);
    background: var(--neon-cyan);
    color: var(--primary-bg);
    box-shadow: 0 0 15px var(--neon-cyan);
}

/* Mission Section */
.mission-section {
    padding: 5rem 0;
    background: var(--primary-bg);
}

.mission-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--neon-orange);
    text-shadow: 0 0 20px var(--neon-orange);
    margin-bottom: 2rem;
}

.mission-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.mission-cta {
    margin-top: 2rem;
}

.mission-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.mission-graphic {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    width: 250px;
    height: 250px;
}

.graphic-element {
    background: var(--gradient-secondary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--primary-bg);
    animation: pulse 2s ease-in-out infinite;
    box-shadow: 0 5px 20px rgba(0, 255, 0, 0.3);
}

.graphic-element:nth-child(1) { animation-delay: 0s; }
.graphic-element:nth-child(2) { animation-delay: 0.3s; }
.graphic-element:nth-child(3) { animation-delay: 0.6s; }
.graphic-element:nth-child(4) { animation-delay: 0.9s; }
.graphic-element:nth-child(5) { animation-delay: 1.2s; }
.graphic-element:nth-child(6) { animation-delay: 1.5s; }

/* Responsive Design */
@media (max-width: 1024px) {
    .story-content,
    .mission-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .story-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .about-hero {
        height: 50vh;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .story-text h2,
    .mission-text h2 {
        font-size: 2rem;
    }
    
    .story-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .values-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .geometric-showcase,
    .mission-graphic {
        width: 200px;
        height: 200px;
    }
    
    .showcase-item,
    .graphic-element {
        font-size: 2rem;
    }
}
