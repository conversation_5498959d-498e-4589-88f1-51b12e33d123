// Skeletal Shenanigans Store - JavaScript
// Geometry Dash inspired e-commerce functionality

// Global variables
let cart = [];
let products = [];

// Sample products data inspired by Geometry Dash
const sampleProducts = [
  {
    id: 1,
    name: "Neon Cube Gaming Mouse",
    description:
      "Precision gaming mouse with RGB lighting and geometric design",
    price: 79.99,
    icon: "🖱️",
  },
  {
    id: 2,
    name: "Skeletal Mechanical Keyboard",
    description:
      "Mechanical keyboard with bone-white keycaps and cyan backlighting",
    price: 149.99,
    icon: "⌨️",
  },
  {
    id: 3,
    name: "Geometry Dash Headset",
    description: "Premium gaming headset with spatial audio and neon accents",
    price: 199.99,
    icon: "🎧",
  },
  {
    id: 4,
    name: "Triangular Gaming Pad",
    description: "Large gaming mousepad with geometric patterns",
    price: 39.99,
    icon: "🔺",
  },
  {
    id: 5,
    name: "Neon Gaming Chair",
    description: "Ergonomic gaming chair with LED strips and skeletal design",
    price: 399.99,
    icon: "🪑",
  },
  {
    id: 6,
    name: "Cube Monitor Stand",
    description: "Adjustable monitor stand with geometric cube design",
    price: 89.99,
    icon: "📺",
  },
  {
    id: 7,
    name: "Skeletal Phone Case",
    description: "Protective phone case with glow-in-the-dark skeletal pattern",
    price: 24.99,
    icon: "📱",
  },
  {
    id: 8,
    name: "Geometry Dash T-Shirt",
    description: "Premium cotton t-shirt with Skeletal Shenanigans design",
    price: 29.99,
    icon: "👕",
  },
];

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  products = [...sampleProducts];
  loadProducts();
  updateCartCount();
  initializeAnimations();
  setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
  // Smooth scrolling for navigation links
  document.querySelectorAll(".nav-link").forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href");
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        targetSection.scrollIntoView({ behavior: "smooth" });
      }
    });
  });

  // Navbar scroll effect
  window.addEventListener("scroll", function () {
    const navbar = document.querySelector(".navbar");
    if (window.scrollY > 100) {
      navbar.style.background = "rgba(10, 10, 10, 0.98)";
    } else {
      navbar.style.background = "rgba(10, 10, 10, 0.95)";
    }
  });
}

// Load products into the grid
function loadProducts() {
  const productsGrid = document.getElementById("productsGrid");
  productsGrid.innerHTML = "";

  products.forEach((product) => {
    const productCard = createProductCard(product);
    productsGrid.appendChild(productCard);
  });
}

// Create product card element
function createProductCard(product) {
  const card = document.createElement("div");
  card.className = "product-card";
  card.innerHTML = `
        <div class="product-image">
            ${product.icon}
        </div>
        <h3 class="product-title">${product.name}</h3>
        <p class="product-description">${product.description}</p>
        <div class="product-price">$${product.price.toFixed(2)}</div>
        <button class="add-to-cart" onclick="addToCart(${product.id})">
            Add to Cart
        </button>
    `;
  return card;
}

// Add product to cart
function addToCart(productId) {
  const product = products.find((p) => p.id === productId);
  if (!product) return;

  const existingItem = cart.find((item) => item.id === productId);
  if (existingItem) {
    existingItem.quantity += 1;
  } else {
    cart.push({ ...product, quantity: 1 });
  }

  updateCartCount();
  updateCartDisplay();
  showAddToCartAnimation();
}

// Remove product from cart
function removeFromCart(productId) {
  cart = cart.filter((item) => item.id !== productId);
  updateCartCount();
  updateCartDisplay();
}

// Update product quantity in cart
function updateQuantity(productId, change) {
  const item = cart.find((item) => item.id === productId);
  if (!item) return;

  item.quantity += change;
  if (item.quantity <= 0) {
    removeFromCart(productId);
  } else {
    updateCartCount();
    updateCartDisplay();
  }
}

// Update cart count display
function updateCartCount() {
  const cartCount = document.querySelector(".cart-count");
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  cartCount.textContent = totalItems;
}

// Update cart modal display
function updateCartDisplay() {
  const cartItems = document.getElementById("cartItems");
  const cartTotal = document.getElementById("cartTotal");

  if (cart.length === 0) {
    cartItems.innerHTML =
      '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">Your cart is empty</p>';
    cartTotal.textContent = "0.00";
    return;
  }

  cartItems.innerHTML = "";
  let total = 0;

  cart.forEach((item) => {
    const cartItem = document.createElement("div");
    cartItem.className = "cart-item";
    cartItem.innerHTML = `
            <div class="cart-item-image">${item.icon}</div>
            <div class="cart-item-details">
                <div class="cart-item-title">${item.name}</div>
                <div class="cart-item-price">$${item.price.toFixed(2)}</div>
            </div>
            <div class="cart-item-quantity">
                <button class="quantity-btn" onclick="updateQuantity(${
                  item.id
                }, -1)">-</button>
                <span>${item.quantity}</span>
                <button class="quantity-btn" onclick="updateQuantity(${
                  item.id
                }, 1)">+</button>
            </div>
        `;
    cartItems.appendChild(cartItem);
    total += item.price * item.quantity;
  });

  cartTotal.textContent = total.toFixed(2);
}

// Toggle cart modal
function toggleCart() {
  const cartModal = document.getElementById("cartModal");
  cartModal.classList.toggle("active");
  if (cartModal.classList.contains("active")) {
    updateCartDisplay();
  }
}

// Toggle mobile menu
function toggleMenu() {
  const navMenu = document.querySelector(".nav-menu");
  const hamburger = document.querySelector(".hamburger");
  navMenu.classList.toggle("active");
  hamburger.classList.toggle("active");
}

// Scroll to products section
function scrollToProducts() {
  document.getElementById("products").scrollIntoView({ behavior: "smooth" });
}

// Checkout function
function checkout() {
  if (cart.length === 0) {
    alert("Your cart is empty!");
    return;
  }

  // Store cart data in localStorage for checkout page
  localStorage.setItem("skeletalCart", JSON.stringify(cart));

  // Redirect to checkout page
  window.location.href = "checkout.html";
}

// Show add to cart animation
function showAddToCartAnimation() {
  const cartIcon = document.querySelector(".cart-icon");
  cartIcon.style.transform = "scale(1.2)";
  cartIcon.style.background = "var(--neon-green)";
  cartIcon.style.color = "var(--primary-bg)";

  setTimeout(() => {
    cartIcon.style.transform = "scale(1)";
    cartIcon.style.background = "transparent";
    cartIcon.style.color = "var(--text-primary)";
  }, 300);
}

// Initialize animations and effects
function initializeAnimations() {
  // Parallax effect for geometric shapes
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll(".shape");

    shapes.forEach((shape, index) => {
      const speed = 0.5 + index * 0.1;
      shape.style.transform = `translateY(${scrolled * speed}px) rotate(${
        scrolled * 0.1
      }deg)`;
    });
  });

  // Intersection Observer for animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = "1";
        entry.target.style.transform = "translateY(0)";
      }
    });
  }, observerOptions);

  // Observe product cards for scroll animations
  setTimeout(() => {
    document.querySelectorAll(".product-card").forEach((card) => {
      card.style.opacity = "0";
      card.style.transform = "translateY(50px)";
      card.style.transition = "all 0.6s ease";
      observer.observe(card);
    });
  }, 100);
}
