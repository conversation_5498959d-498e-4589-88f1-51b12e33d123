// About Page JavaScript
// Animations and interactions for the about page

document.addEventListener("DOMContentLoaded", function () {
  initializeAnimations();
  setupScrollAnimations();
  animateStats();
});

// Initialize page animations
function initializeAnimations() {
  // Parallax effect for geometric shapes
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll(".shape");

    shapes.forEach((shape, index) => {
      const speed = 0.3 + index * 0.1;
      shape.style.transform = `translateY(${scrolled * speed}px) rotate(${
        scrolled * 0.05
      }deg)`;
    });
  });
}

// Setup scroll-triggered animations
function setupScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-in");
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const animateElements = document.querySelectorAll(
    ".value-card, .team-member, .stat-item"
  );
  animateElements.forEach((element, index) => {
    element.style.opacity = "0";
    element.style.transform = "translateY(50px)";
    element.style.transition = `all 0.6s ease ${index * 0.1}s`;
    observer.observe(element);
  });
}

// Animate statistics numbers
function animateStats() {
  const statNumbers = document.querySelectorAll(".stat-number");

  const observer = new IntersectionObserver(
    function (entries) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target;
          const finalValue = target.textContent;

          // Extract number from text (e.g., "50K+" -> 50)
          const numericValue = parseInt(finalValue.replace(/[^\d]/g, ""));
          const suffix = finalValue.replace(/[\d]/g, "");

          animateNumber(target, 0, numericValue, suffix, 2000);
          observer.unobserve(target);
        }
      });
    },
    { threshold: 0.5 }
  );

  statNumbers.forEach((stat) => observer.observe(stat));
}

// Animate number counting
function animateNumber(element, start, end, suffix, duration) {
  const startTime = performance.now();

  function updateNumber(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = Math.floor(start + (end - start) * easeOutQuart);

    element.textContent = current + suffix;

    if (progress < 1) {
      requestAnimationFrame(updateNumber);
    }
  }

  requestAnimationFrame(updateNumber);
}

// Toggle mobile menu
function toggleMenu() {
  const navMenu = document.querySelector(".nav-menu");
  const hamburger = document.querySelector(".hamburger");
  navMenu.classList.toggle("active");
  hamburger.classList.toggle("active");
}

// Cart functionality (basic for navigation consistency)
let cart = JSON.parse(localStorage.getItem("skeletalCart")) || [];

function toggleCart() {
  const cartModal = document.getElementById("cartModal");
  cartModal.classList.toggle("active");
  if (cartModal.classList.contains("active")) {
    updateCartDisplay();
  }
}

function updateCartDisplay() {
  const cartItems = document.getElementById("cartItems");
  const cartTotal = document.getElementById("cartTotal");

  if (cart.length === 0) {
    cartItems.innerHTML =
      '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">Your cart is empty</p>';
    cartTotal.textContent = "0.00";
    return;
  }

  cartItems.innerHTML = "";
  let total = 0;

  cart.forEach((item) => {
    const cartItem = document.createElement("div");
    cartItem.className = "cart-item";
    cartItem.innerHTML = `
            <div class="cart-item-image">${item.icon}</div>
            <div class="cart-item-details">
                <div class="cart-item-title">${item.name}</div>
                <div class="cart-item-price">$${item.price.toFixed(2)}</div>
            </div>
            <div class="cart-item-quantity">
                <span>${item.quantity}</span>
            </div>
        `;
    cartItems.appendChild(cartItem);
    total += item.price * item.quantity;
  });

  cartTotal.textContent = total.toFixed(2);
}

function checkout() {
  localStorage.setItem("skeletalCart", JSON.stringify(cart));
  window.location.href = "checkout.html";
}

function updateCartCount() {
  const cartCount = document.querySelector(".cart-count");
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  cartCount.textContent = totalItems;
}

// Initialize cart count on page load
updateCartCount();

// Add CSS for animation states
const style = document.createElement("style");
style.textContent = `
    .animate-in {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
    
    .value-card:hover .value-icon {
        transform: scale(1.1) rotate(10deg);
        transition: transform 0.3s ease;
    }
    
    .team-member:hover .member-avatar {
        transform: scale(1.05);
        transition: transform 0.3s ease;
    }
    
    .showcase-item:hover,
    .graphic-element:hover {
        transform: scale(1.1) rotate(5deg);
        transition: transform 0.3s ease;
    }
`;
document.head.appendChild(style);
