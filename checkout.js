// Checkout Page JavaScript
// Geometry Dash Skeletal Shenanigans inspired checkout functionality

let checkoutCart = [];
let orderTotal = 0;

// Initialize checkout page
document.addEventListener("DOMContentLoaded", function () {
  loadCartFromStorage();
  displayOrderSummary();
  calculateTotals();
  setupFormValidation();
  setupPaymentMethods();
  initializeAnimations();
});

// Load cart data from localStorage or URL parameters
function loadCartFromStorage() {
  const urlParams = new URLSearchParams(window.location.search);
  const cartData = urlParams.get("cart");

  if (cartData) {
    try {
      checkoutCart = JSON.parse(decodeURIComponent(cartData));
    } catch (e) {
      console.error("Error parsing cart data:", e);
      checkoutCart = [];
    }
  } else {
    // Fallback to localStorage
    const storedCart = localStorage.getItem("skeletalCart");
    if (storedCart) {
      try {
        checkoutCart = JSON.parse(storedCart);
      } catch (e) {
        console.error("Error parsing stored cart:", e);
        checkoutCart = [];
      }
    }
  }

  // If no cart data, redirect back to store
  if (checkoutCart.length === 0) {
    alert("Your cart is empty! Redirecting to store...");
    window.location.href = "index.html";
  }
}

// Display order summary
function displayOrderSummary() {
  const checkoutItems = document.getElementById("checkoutItems");
  checkoutItems.innerHTML = "";

  checkoutCart.forEach((item) => {
    const orderItem = document.createElement("div");
    orderItem.className = "order-item";
    orderItem.innerHTML = `
            <div class="order-item-image">${item.icon}</div>
            <div class="order-item-details">
                <div class="order-item-name">${item.name}</div>
                <div class="order-item-quantity">Qty: ${item.quantity}</div>
            </div>
            <div class="order-item-price">$${(
              item.price * item.quantity
            ).toFixed(2)}</div>
        `;
    checkoutItems.appendChild(orderItem);
  });
}

// Calculate totals
function calculateTotals() {
  const subtotal = checkoutCart.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const shipping = subtotal > 100 ? 0 : 9.99; // Free shipping over $100
  const taxRate = 0.08; // 8% tax
  const tax = subtotal * taxRate;
  const total = subtotal + shipping + tax;

  document.getElementById("subtotal").textContent = `$${subtotal.toFixed(2)}`;
  document.getElementById("shipping").textContent =
    shipping === 0 ? "FREE" : `$${shipping.toFixed(2)}`;
  document.getElementById("tax").textContent = `$${tax.toFixed(2)}`;
  document.getElementById("finalTotal").textContent = `$${total.toFixed(2)}`;

  orderTotal = total;
}

// Setup form validation
function setupFormValidation() {
  const form = document.getElementById("checkoutFormElement");
  const inputs = form.querySelectorAll("input, select");

  inputs.forEach((input) => {
    input.addEventListener("blur", validateField);
    input.addEventListener("input", function () {
      if (this.classList.contains("error")) {
        validateField.call(this);
      }
    });
  });

  // Card number formatting
  const cardNumber = document.getElementById("cardNumber");
  cardNumber.addEventListener("input", function () {
    let value = this.value.replace(/\s/g, "").replace(/[^0-9]/gi, "");
    let formattedValue = value.match(/.{1,4}/g)?.join(" ") || value;
    this.value = formattedValue;
  });

  // Expiry date formatting
  const expiryDate = document.getElementById("expiryDate");
  expiryDate.addEventListener("input", function () {
    let value = this.value.replace(/\D/g, "");
    if (value.length >= 2) {
      value = value.substring(0, 2) + "/" + value.substring(2, 4);
    }
    this.value = value;
  });

  // CVV validation
  const cvv = document.getElementById("cvv");
  cvv.addEventListener("input", function () {
    this.value = this.value.replace(/\D/g, "");
  });
}

// Validate individual field
function validateField() {
  const field = this;
  const value = field.value.trim();
  let isValid = true;
  let errorMessage = "";

  // Remove existing error styling
  field.classList.remove("error");
  const existingError = field.parentNode.querySelector(".error-message");
  if (existingError) {
    existingError.remove();
  }

  // Validation rules
  switch (field.type) {
    case "email":
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = "Please enter a valid email address";
      }
      break;
    case "tel":
      const phoneRegex = /^[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(value) || value.length < 10) {
        isValid = false;
        errorMessage = "Please enter a valid phone number";
      }
      break;
    default:
      if (field.required && !value) {
        isValid = false;
        errorMessage = "This field is required";
      }
  }

  // Special validations
  if (field.id === "cardNumber") {
    const cardRegex = /^[\d\s]{13,19}$/;
    if (!cardRegex.test(value.replace(/\s/g, ""))) {
      isValid = false;
      errorMessage = "Please enter a valid card number";
    }
  }

  if (field.id === "expiryDate") {
    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!expiryRegex.test(value)) {
      isValid = false;
      errorMessage = "Please enter a valid expiry date (MM/YY)";
    }
  }

  if (field.id === "cvv") {
    if (value.length < 3 || value.length > 4) {
      isValid = false;
      errorMessage = "Please enter a valid CVV";
    }
  }

  // Show error if invalid
  if (!isValid) {
    field.classList.add("error");
    const errorDiv = document.createElement("div");
    errorDiv.className = "error-message";
    errorDiv.textContent = errorMessage;
    errorDiv.style.color = "var(--neon-orange)";
    errorDiv.style.fontSize = "0.8rem";
    errorDiv.style.marginTop = "0.25rem";
    field.parentNode.appendChild(errorDiv);
  }

  return isValid;
}

// Setup payment methods
function setupPaymentMethods() {
  const paymentMethods = document.querySelectorAll(".payment-method");

  paymentMethods.forEach((method) => {
    method.addEventListener("click", function () {
      // Remove active class from all methods
      paymentMethods.forEach((m) => m.classList.remove("active"));
      // Add active class to clicked method
      this.classList.add("active");

      // Show/hide payment forms based on selection
      const selectedMethod = this.dataset.method;
      updatePaymentForm(selectedMethod);
    });
  });
}

// Update payment form based on selected method
function updatePaymentForm(method) {
  const cardForm = document.getElementById("cardForm");

  switch (method) {
    case "card":
      cardForm.style.display = "block";
      break;
    case "paypal":
      cardForm.style.display = "none";
      // In a real implementation, you'd integrate PayPal SDK here
      break;
    case "crypto":
      cardForm.style.display = "none";
      // In a real implementation, you'd integrate crypto payment here
      break;
  }
}

// Process payment
function processPayment(event) {
  event.preventDefault();

  // Validate all fields
  const form = document.getElementById("checkoutFormElement");
  const inputs = form.querySelectorAll("input[required], select[required]");
  let isFormValid = true;

  inputs.forEach((input) => {
    if (!validateField.call(input)) {
      isFormValid = false;
    }
  });

  if (!isFormValid) {
    alert("Please correct the errors in the form before proceeding.");
    return;
  }

  // Show loading state
  const submitBtn = document.querySelector(".complete-order-btn");
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> PROCESSING...';
  submitBtn.disabled = true;

  // Simulate payment processing
  setTimeout(() => {
    // Generate order number
    const orderNumber = "SK" + Date.now().toString().slice(-8);

    // Show success modal
    showSuccessModal(orderNumber);

    // Clear cart
    localStorage.removeItem("skeletalCart");

    // Reset button
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 3000);
}

// Show success modal
function showSuccessModal(orderNumber) {
  const modal = document.getElementById("successModal");
  const orderNumberSpan = document.getElementById("orderNumber");

  orderNumberSpan.textContent = orderNumber;
  modal.classList.add("active");

  // Add success animation
  const successIcon = modal.querySelector(".success-animation i");
  successIcon.style.animation = "successPulse 1s ease-in-out";
}

// Go back to store
function goToStore() {
  window.location.href = "index.html";
}

// Go back function
function goBack() {
  window.history.back();
}

// Toggle mobile menu
function toggleMenu() {
  const navMenu = document.querySelector(".nav-menu");
  const hamburger = document.querySelector(".hamburger");
  navMenu.classList.toggle("active");
  hamburger.classList.toggle("active");
}

// Initialize animations
function initializeAnimations() {
  // Parallax effect for geometric shapes
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll(".shape");

    shapes.forEach((shape, index) => {
      const speed = 0.3 + index * 0.1;
      shape.style.transform = `translateY(${scrolled * speed}px) rotate(${
        scrolled * 0.05
      }deg)`;
    });
  });

  // Form field animations
  const formGroups = document.querySelectorAll(".form-group");
  formGroups.forEach((group, index) => {
    group.style.opacity = "0";
    group.style.transform = "translateY(20px)";
    group.style.transition = "all 0.6s ease";

    setTimeout(() => {
      group.style.opacity = "1";
      group.style.transform = "translateY(0)";
    }, index * 100);
  });
}

// Add error styles to CSS
const style = document.createElement("style");
style.textContent = `
    .form-group input.error,
    .form-group select.error {
        border-color: var(--neon-orange) !important;
        box-shadow: 0 0 20px rgba(255, 102, 0, 0.3) !important;
    }
`;
document.head.appendChild(style);
