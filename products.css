/* Products Page Specific Styles */

/* Products Hero */
.products-hero {
  height: 50vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

/* Active navigation link */
.nav-link.active {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.nav-link.active::after {
  width: 100%;
}

/* Filters Section */
.filters-section {
  background: var(--secondary-bg);
  padding: 2rem 0;
  border-bottom: 2px solid var(--border-glow);
}

.filters-container {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 2rem;
  align-items: center;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-box input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.search-box i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1.2rem;
}

.filter-categories {
  display: flex;
  gap: 1rem;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 6px;
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Rajdhani", sans-serif;
}

.filter-btn:hover,
.filter-btn.active {
  border-color: var(--neon-cyan);
  background: rgba(0, 255, 255, 0.1);
  color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.sort-options select {
  padding: 0.8rem 1rem;
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-options select:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

/* Products Main */
.products-main {
  padding: 3rem 0;
  background: var(--primary-bg);
  min-height: 60vh;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.results-count {
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-weight: 600;
}

/* Enhanced Product Cards */
.product-card {
  position: relative;
  cursor: pointer;
}

.product-card .quick-view-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-bg);
  cursor: pointer;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.product-card:hover .quick-view-btn {
  opacity: 1;
  transform: scale(1);
}

.product-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--neon-purple);
  color: var(--primary-bg);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Load More Button */
.load-more-container {
  text-align: center;
  margin-top: 3rem;
}

.load-more-btn {
  background: var(--gradient-secondary);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-bg);
  cursor: pointer;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Orbitron", monospace;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 255, 0, 0.3);
}

.load-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Quick View Modal */
.quick-view-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 2500;
  display: none;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.quick-view-modal.active {
  display: flex;
}

.quick-view-content {
  background: var(--secondary-bg);
  border: 2px solid var(--neon-cyan);
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 255, 255, 0.3);
}

.close-quick-view {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  z-index: 10;
}

.close-quick-view:hover {
  background: var(--neon-orange);
  color: var(--primary-bg);
}

.quick-view-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.product-image-large {
  width: 100%;
  height: 300px;
  background: var(--gradient-secondary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 6rem;
  color: var(--primary-bg);
  position: relative;
  overflow: hidden;
}

.quick-view-details h3 {
  font-family: "Orbitron", monospace;
  font-size: 1.8rem;
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.quick-view-details p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.quick-view-price {
  font-size: 2rem;
  font-weight: 900;
  color: var(--neon-green);
  text-shadow: 0 0 15px var(--neon-green);
  margin-bottom: 1.5rem;
}

.quick-view-features h4 {
  color: var(--neon-purple);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 700;
}

.quick-view-features ul {
  list-style: none;
  margin-bottom: 2rem;
}

.quick-view-features li {
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  position: relative;
  padding-left: 1rem;
}

.quick-view-features li::before {
  content: "▶";
  position: absolute;
  left: 0;
  color: var(--neon-cyan);
}

.quick-view-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 6px;
  padding: 0.5rem;
}

.qty-btn {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.qty-btn:hover {
  background: var(--neon-cyan);
  color: var(--primary-bg);
}

.quantity-selector span {
  min-width: 30px;
  text-align: center;
  font-weight: 700;
  color: var(--text-primary);
}

.add-to-cart-large {
  flex: 1;
  background: var(--gradient-primary);
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-bg);
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Orbitron", monospace;
}

.add-to-cart-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-categories {
    justify-content: center;
    flex-wrap: wrap;
  }

  .quick-view-grid {
    grid-template-columns: 1fr;
  }

  .product-image-large {
    height: 250px;
    font-size: 4rem;
  }
}

@media (max-width: 768px) {
  .products-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .filter-btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .quick-view-content {
    margin: 1rem;
    max-height: 85vh;
  }

  .quick-view-grid {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .quick-view-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .add-to-cart-large {
    width: 100%;
  }
}

/* Product Card Animations */
.product-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(30px);
}

.product-card:nth-child(1) {
  animation-delay: 0.1s;
}
.product-card:nth-child(2) {
  animation-delay: 0.2s;
}
.product-card:nth-child(3) {
  animation-delay: 0.3s;
}
.product-card:nth-child(4) {
  animation-delay: 0.4s;
}
.product-card:nth-child(5) {
  animation-delay: 0.5s;
}
.product-card:nth-child(6) {
  animation-delay: 0.6s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Filter Animation */
.filter-btn {
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.filter-btn:hover::before {
  left: 100%;
}
