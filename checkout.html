<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Skeletal Shenanigans Store</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="checkout.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-skull"></i>
                <span>SKELETAL STORE</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">BACK TO STORE</a></li>
            </ul>
            <div class="nav-icons">
                <div class="cart-icon" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- Checkout Hero -->
    <section class="checkout-hero">
        <div class="hero-bg">
            <div class="geometric-shapes">
                <div class="shape triangle"></div>
                <div class="shape square"></div>
                <div class="shape diamond"></div>
                <div class="shape hexagon"></div>
            </div>
        </div>
        <div class="checkout-header">
            <h1 class="checkout-title">
                <span class="glitch" data-text="SECURE">SECURE</span>
                <span class="glitch" data-text="CHECKOUT">CHECKOUT</span>
            </h1>
            <div class="security-badges">
                <div class="badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>SSL SECURED</span>
                </div>
                <div class="badge">
                    <i class="fas fa-lock"></i>
                    <span>256-BIT ENCRYPTION</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Checkout Content -->
    <section class="checkout-section">
        <div class="container">
            <div class="checkout-grid">
                <!-- Order Summary -->
                <div class="order-summary">
                    <h2 class="section-title">
                        <i class="fas fa-receipt"></i>
                        ORDER SUMMARY
                    </h2>
                    <div class="order-items" id="checkoutItems">
                        <!-- Items will be loaded here -->
                    </div>
                    <div class="order-totals">
                        <div class="total-line">
                            <span>Subtotal:</span>
                            <span id="subtotal">$0.00</span>
                        </div>
                        <div class="total-line">
                            <span>Shipping:</span>
                            <span id="shipping">$9.99</span>
                        </div>
                        <div class="total-line">
                            <span>Tax:</span>
                            <span id="tax">$0.00</span>
                        </div>
                        <div class="total-line final-total">
                            <span>TOTAL:</span>
                            <span id="finalTotal">$0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Checkout Form -->
                <div class="checkout-form">
                    <form id="checkoutFormElement" onsubmit="processPayment(event)">
                        <!-- Shipping Information -->
                        <div class="form-section">
                            <h3 class="form-title">
                                <i class="fas fa-shipping-fast"></i>
                                SHIPPING INFORMATION
                            </h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="firstName">First Name</label>
                                    <input type="text" id="firstName" name="firstName" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group full-width">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" name="email" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group full-width">
                                    <label for="address">Street Address</label>
                                    <input type="text" id="address" name="address" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <input type="text" id="city" name="city" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group">
                                    <label for="state">State</label>
                                    <select id="state" name="state" required>
                                        <option value="">Select State</option>
                                        <option value="CA">California</option>
                                        <option value="NY">New York</option>
                                        <option value="TX">Texas</option>
                                        <option value="FL">Florida</option>
                                        <!-- Add more states as needed -->
                                    </select>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group">
                                    <label for="zipCode">ZIP Code</label>
                                    <input type="text" id="zipCode" name="zipCode" pattern="[0-9]{5}" required>
                                    <div class="input-glow"></div>
                                </div>
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" required>
                                    <div class="input-glow"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="form-section">
                            <h3 class="form-title">
                                <i class="fas fa-credit-card"></i>
                                PAYMENT INFORMATION
                            </h3>
                            <div class="payment-methods">
                                <div class="payment-method active" data-method="card">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Credit Card</span>
                                </div>
                                <div class="payment-method" data-method="paypal">
                                    <i class="fab fa-paypal"></i>
                                    <span>PayPal</span>
                                </div>
                                <div class="payment-method" data-method="crypto">
                                    <i class="fab fa-bitcoin"></i>
                                    <span>Crypto</span>
                                </div>
                            </div>
                            
                            <div class="payment-form" id="cardForm">
                                <div class="form-grid">
                                    <div class="form-group full-width">
                                        <label for="cardNumber">Card Number</label>
                                        <input type="text" id="cardNumber" name="cardNumber" placeholder="1234 5678 9012 3456" maxlength="19" required>
                                        <div class="input-glow"></div>
                                        <div class="card-icons">
                                            <i class="fab fa-cc-visa"></i>
                                            <i class="fab fa-cc-mastercard"></i>
                                            <i class="fab fa-cc-amex"></i>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="expiryDate">Expiry Date</label>
                                        <input type="text" id="expiryDate" name="expiryDate" placeholder="MM/YY" maxlength="5" required>
                                        <div class="input-glow"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="cvv">CVV</label>
                                        <input type="text" id="cvv" name="cvv" placeholder="123" maxlength="4" required>
                                        <div class="input-glow"></div>
                                    </div>
                                    <div class="form-group full-width">
                                        <label for="cardName">Name on Card</label>
                                        <input type="text" id="cardName" name="cardName" required>
                                        <div class="input-glow"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-section">
                            <button type="submit" class="complete-order-btn">
                                <span>COMPLETE ORDER</span>
                                <i class="fas fa-rocket"></i>
                            </button>
                            <p class="security-note">
                                <i class="fas fa-shield-alt"></i>
                                Your payment information is encrypted and secure
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Modal -->
    <div id="successModal" class="success-modal">
        <div class="success-content">
            <div class="success-animation">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>ORDER CONFIRMED!</h2>
            <p>Thank you for your purchase! Your order has been successfully processed.</p>
            <div class="order-number">
                Order #: <span id="orderNumber"></span>
            </div>
            <button class="back-to-store-btn" onclick="goToStore()">
                <span>BACK TO STORE</span>
                <i class="fas fa-home"></i>
            </button>
        </div>
    </div>

    <script src="checkout.js"></script>
</body>
</html>
