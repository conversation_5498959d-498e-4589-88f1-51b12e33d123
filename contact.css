/* Contact Page Specific Styles */

/* Contact Hero */
.contact-hero {
    height: 50vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 80px;
}

/* Contact Info Section */
.contact-info-section {
    padding: 3rem 0;
    background: var(--secondary-bg);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-card {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.info-card:hover::before {
    left: 100%;
}

.info-card:hover {
    border-color: var(--neon-cyan);
    box-shadow: 0 15px 40px rgba(0, 255, 255, 0.2);
    transform: translateY(-5px);
}

.info-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--primary-bg);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.info-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.info-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-details span {
    color: var(--neon-green);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Contact Form Section */
.contact-form-section {
    padding: 5rem 0;
    background: var(--primary-bg);
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.contact-form {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--neon-cyan);
    text-shadow: 0 0 10px var(--neon-cyan);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: var(--secondary-bg);
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--neon-cyan);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.input-glow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.form-group input:focus + .input-glow,
.form-group select:focus + .input-glow,
.form-group textarea:focus + .input-glow {
    width: 100%;
}

.submit-btn {
    width: 100%;
    background: var(--gradient-primary);
    border: none;
    padding: 1.2rem;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-bg);
    cursor: pointer;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-family: 'Orbitron', monospace;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

/* Contact Details */
.contact-details {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    padding: 2rem;
    height: fit-content;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-glow);
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-bg);
    flex-shrink: 0;
}

.contact-text h4 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--neon-purple);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.contact-text p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Social Section */
.social-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-glow);
}

.social-section h3 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--neon-orange);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.social-links-large {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--secondary-bg);
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    border-color: var(--neon-cyan);
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* FAQ Section */
.faq-section {
    padding: 5rem 0;
    background: var(--secondary-bg);
}

.faq-grid {
    display: grid;
    gap: 1rem;
    margin-top: 3rem;
}

.faq-item {
    background: var(--accent-bg);
    border: 2px solid var(--border-glow);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--neon-cyan);
}

.faq-question {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: rgba(0, 255, 255, 0.1);
}

.faq-question i {
    color: var(--neon-cyan);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 0 1.5rem 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Success Modal */
.success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
}

.success-modal.active {
    display: flex;
}

.success-content {
    background: var(--secondary-bg);
    border: 2px solid var(--neon-green);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 255, 0, 0.3);
}

.success-animation i {
    font-size: 4rem;
    color: var(--neon-green);
    animation: successPulse 2s infinite;
    margin-bottom: 1rem;
}

.success-content h2 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--neon-green);
    text-shadow: 0 0 20px var(--neon-green);
    margin-bottom: 1rem;
}

.success-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.close-modal-btn {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary-bg);
    cursor: pointer;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-family: 'Orbitron', monospace;
}

.close-modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .contact-hero {
        height: 40vh;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-info-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-form,
    .contact-details {
        padding: 1.5rem;
    }
    
    .social-links-large {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .social-link {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
}
