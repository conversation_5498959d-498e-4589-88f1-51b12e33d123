/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a1a;
  --accent-bg: #2a2a2a;
  --neon-cyan: #00ffff;
  --neon-purple: #ff00ff;
  --neon-green: #00ff00;
  --neon-orange: #ff6600;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-glow: #333333;
  --gradient-primary: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple)
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--neon-green),
    var(--neon-orange)
  );
}

body {
  font-family: "Rajdhani", sans-serif;
  background: var(--primary-bg);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid var(--neon-cyan);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  align-items: center;
  font-family: "Orbitron", monospace;
  font-weight: 900;
  font-size: 1.5rem;
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.nav-logo i {
  margin-right: 10px;
  font-size: 2rem;
  animation: pulse 2s infinite;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
}

.nav-link:hover {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-icons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart-icon {
  position: relative;
  cursor: pointer;
  padding: 0.5rem;
  border: 2px solid var(--neon-purple);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.cart-icon:hover {
  background: var(--neon-purple);
  color: var(--primary-bg);
  box-shadow: 0 0 20px var(--neon-purple);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--neon-orange);
  color: var(--primary-bg);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--neon-cyan);
  transition: all 0.3s ease;
}

/* Hero Section */
.hero {
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    var(--secondary-bg) 0%,
    var(--primary-bg) 100%
  );
}

.geometric-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 87px solid var(--neon-cyan);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.square {
  width: 80px;
  height: 80px;
  background: var(--neon-purple);
  transform: rotate(45deg);
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.diamond {
  width: 60px;
  height: 60px;
  background: var(--neon-green);
  transform: rotate(45deg);
  top: 30%;
  right: 30%;
  animation-delay: 2s;
}

.hexagon {
  width: 70px;
  height: 40px;
  background: var(--neon-orange);
  position: relative;
  top: 70%;
  left: 20%;
  animation-delay: 3s;
}

.hexagon:before,
.hexagon:after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 35px solid transparent;
  border-right: 35px solid transparent;
}

.hexagon:before {
  bottom: 100%;
  border-bottom: 20px solid var(--neon-orange);
}

.hexagon:after {
  top: 100%;
  border-top: 20px solid var(--neon-orange);
}

.hero-content {
  text-align: center;
  z-index: 2;
}

.hero-title {
  font-family: "Orbitron", monospace;
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.glitch {
  position: relative;
  color: var(--text-primary);
  text-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan),
    0 0 30px var(--neon-cyan);
  animation: glitch 2s infinite;
}

.glitch:nth-child(2) {
  animation-delay: 0.5s;
  color: var(--neon-purple);
  text-shadow: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple),
    0 0 30px var(--neon-purple);
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-weight: 300;
}

.cta-button {
  background: var(--gradient-primary);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-bg);
  cursor: pointer;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Orbitron", monospace;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid var(--neon-cyan);
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

/* Animations */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes glitch {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Products Section */
.products-section {
  padding: 100px 0;
  background: var(--secondary-bg);
  position: relative;
}

.section-title {
  font-family: "Orbitron", monospace;
  font-size: 2.5rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  color: var(--text-primary);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.title-line {
  width: 100px;
  height: 2px;
  background: var(--gradient-primary);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.product-card {
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.product-card:hover::before {
  left: 100%;
}

.product-card:hover {
  border-color: var(--neon-cyan);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  background: var(--gradient-secondary);
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--primary-bg);
  position: relative;
  overflow: hidden;
}

.product-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.product-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--neon-green);
  margin-bottom: 1rem;
  text-shadow: 0 0 10px var(--neon-green);
}

.add-to-cart {
  width: 100%;
  background: var(--gradient-primary);
  border: none;
  padding: 0.8rem;
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary-bg);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Rajdhani", sans-serif;
}

.add-to-cart:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
}

/* About Section */
.about-section {
  padding: 100px 0;
  background: var(--primary-bg);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text h2 {
  font-family: "Orbitron", monospace;
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  color: var(--neon-purple);
  text-shadow: 0 0 10px var(--neon-purple);
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  color: var(--text-secondary);
}

.stats {
  display: flex;
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 900;
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.about-visual {
  height: 400px;
  position: relative;
}

.geometric-pattern {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--neon-cyan) 25%, transparent 25%),
    linear-gradient(-45deg, var(--neon-purple) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--neon-green) 75%),
    linear-gradient(-45deg, transparent 75%, var(--neon-orange) 75%);
  background-size: 40px 40px;
  background-position: 0 0, 0 20px, 20px -20px, -20px 0px;
  opacity: 0.3;
  border-radius: 12px;
  animation: patternShift 10s linear infinite;
}

@keyframes patternShift {
  0% {
    background-position: 0 0, 0 20px, 20px -20px, -20px 0px;
  }
  100% {
    background-position: 40px 40px, 40px 60px, 60px 20px, 20px 40px;
  }
}

/* Cart Modal */
.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
}

.cart-modal.active {
  display: flex;
}

.cart-content {
  background: var(--secondary-bg);
  border: 2px solid var(--neon-cyan);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 255, 255, 0.3);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 2px solid var(--border-glow);
}

.cart-header h3 {
  font-family: "Orbitron", monospace;
  font-weight: 900;
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.close-cart {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.close-cart:hover {
  background: var(--neon-purple);
  color: var(--primary-bg);
}

.cart-items {
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-glow);
  transition: all 0.3s ease;
}

.cart-item:hover {
  background: var(--accent-bg);
}

.cart-item-image {
  width: 60px;
  height: 60px;
  background: var(--gradient-secondary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--primary-bg);
}

.cart-item-details {
  flex: 1;
}

.cart-item-title {
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.cart-item-price {
  color: var(--neon-green);
  font-weight: 600;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  background: var(--accent-bg);
  border: 1px solid var(--border-glow);
  color: var(--text-primary);
  width: 30px;
  height: 30px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background: var(--neon-cyan);
  color: var(--primary-bg);
}

.cart-footer {
  padding: 1.5rem;
  border-top: 2px solid var(--border-glow);
}

.cart-total {
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--neon-green);
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px var(--neon-green);
}

.checkout-btn {
  width: 100%;
  background: var(--gradient-primary);
  border: none;
  padding: 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-bg);
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: "Orbitron", monospace;
}

.checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

/* Footer */
.footer {
  background: var(--primary-bg);
  border-top: 2px solid var(--neon-cyan);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  font-family: "Orbitron", monospace;
  font-weight: 900;
  color: var(--neon-purple);
  margin-bottom: 1rem;
  text-shadow: 0 0 10px var(--neon-purple);
}

.footer-section p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-section ul li a:hover {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--accent-bg);
  border: 2px solid var(--border-glow);
  border-radius: 8px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  border-color: var(--neon-cyan);
  background: var(--neon-cyan);
  color: var(--primary-bg);
  box-shadow: 0 0 20px var(--neon-cyan);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-glow);
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 2rem;
    transition: left 0.3s ease;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .stats {
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 1rem;
  }

  .title-line {
    width: 50px;
  }

  .cart-content {
    width: 95%;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .cta-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .about-text h2 {
    font-size: 2rem;
  }

  .stats {
    flex-direction: column;
    gap: 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-cyan);
}
