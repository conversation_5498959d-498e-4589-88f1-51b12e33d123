// Contact Page JavaScript
// Form handling, FAQ interactions, and animations

document.addEventListener("DOMContentLoaded", function () {
  initializeAnimations();
  setupFormValidation();
  setupFAQInteractions();
});

// Initialize page animations
function initializeAnimations() {
  // Parallax effect for geometric shapes
  window.addEventListener("scroll", function () {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll(".shape");

    shapes.forEach((shape, index) => {
      const speed = 0.3 + index * 0.1;
      shape.style.transform = `translateY(${scrolled * speed}px) rotate(${
        scrolled * 0.05
      }deg)`;
    });
  });

  // Animate elements on scroll
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-in");
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const animateElements = document.querySelectorAll(
    ".info-card, .contact-item, .faq-item"
  );
  animateElements.forEach((element, index) => {
    element.style.opacity = "0";
    element.style.transform = "translateY(30px)";
    element.style.transition = `all 0.6s ease ${index * 0.1}s`;
    observer.observe(element);
  });
}

// Setup form validation
function setupFormValidation() {
  const form = document.getElementById("contactForm");
  const inputs = form.querySelectorAll("input, select, textarea");

  inputs.forEach((input) => {
    input.addEventListener("blur", validateField);
    input.addEventListener("input", function () {
      if (this.classList.contains("error")) {
        validateField.call(this);
      }
    });
  });
}

// Validate individual field
function validateField() {
  const field = this;
  const value = field.value.trim();
  let isValid = true;
  let errorMessage = "";

  // Remove existing error styling
  field.classList.remove("error");
  const existingError = field.parentNode.querySelector(".error-message");
  if (existingError) {
    existingError.remove();
  }

  // Validation rules
  if (field.required && !value) {
    isValid = false;
    errorMessage = "This field is required";
  } else if (field.type === "email") {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      isValid = false;
      errorMessage = "Please enter a valid email address";
    }
  }

  // Show error if invalid
  if (!isValid) {
    field.classList.add("error");
    const errorDiv = document.createElement("div");
    errorDiv.className = "error-message";
    errorDiv.textContent = errorMessage;
    errorDiv.style.color = "var(--neon-orange)";
    errorDiv.style.fontSize = "0.8rem";
    errorDiv.style.marginTop = "0.25rem";
    field.parentNode.appendChild(errorDiv);
  }

  return isValid;
}

// Submit contact form
function submitContactForm(event) {
  event.preventDefault();

  // Validate all fields
  const form = document.getElementById("contactForm");
  const inputs = form.querySelectorAll(
    "input[required], select[required], textarea[required]"
  );
  let isFormValid = true;

  inputs.forEach((input) => {
    if (!validateField.call(input)) {
      isFormValid = false;
    }
  });

  if (!isFormValid) {
    // Scroll to first error
    const firstError = form.querySelector(".error");
    if (firstError) {
      firstError.scrollIntoView({ behavior: "smooth", block: "center" });
    }
    return;
  }

  // Show loading state
  const submitBtn = form.querySelector(".submit-btn");
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> SENDING...';
  submitBtn.disabled = true;

  // Simulate form submission
  setTimeout(() => {
    // Show success modal
    showSuccessModal();

    // Reset form
    form.reset();

    // Reset button
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 2000);
}

// Show success modal
function showSuccessModal() {
  const modal = document.getElementById("successModal");
  modal.classList.add("active");
}

// Close success modal
function closeSuccessModal() {
  const modal = document.getElementById("successModal");
  modal.classList.remove("active");
}

// Setup FAQ interactions
function setupFAQInteractions() {
  const faqItems = document.querySelectorAll(".faq-item");

  faqItems.forEach((item) => {
    const question = item.querySelector(".faq-question");
    question.addEventListener("click", function () {
      toggleFAQ(this);
    });
  });
}

// Toggle FAQ item
function toggleFAQ(questionElement) {
  const faqItem = questionElement.parentNode;
  const isActive = faqItem.classList.contains("active");

  // Close all FAQ items
  document.querySelectorAll(".faq-item").forEach((item) => {
    item.classList.remove("active");
  });

  // Open clicked item if it wasn't active
  if (!isActive) {
    faqItem.classList.add("active");
  }
}

// Scroll to FAQ section
function scrollToFAQ() {
  document.querySelector(".faq-section").scrollIntoView({ behavior: "smooth" });
}

// Toggle mobile menu
function toggleMenu() {
  const navMenu = document.querySelector(".nav-menu");
  const hamburger = document.querySelector(".hamburger");
  navMenu.classList.toggle("active");
  hamburger.classList.toggle("active");
}

// Cart functionality (basic for navigation consistency)
let cart = JSON.parse(localStorage.getItem("skeletalCart")) || [];

function toggleCart() {
  const cartModal = document.getElementById("cartModal");
  cartModal.classList.toggle("active");
  if (cartModal.classList.contains("active")) {
    updateCartDisplay();
  }
}

function updateCartDisplay() {
  const cartItems = document.getElementById("cartItems");
  const cartTotal = document.getElementById("cartTotal");

  if (cart.length === 0) {
    cartItems.innerHTML =
      '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">Your cart is empty</p>';
    cartTotal.textContent = "0.00";
    return;
  }

  cartItems.innerHTML = "";
  let total = 0;

  cart.forEach((item) => {
    const cartItem = document.createElement("div");
    cartItem.className = "cart-item";
    cartItem.innerHTML = `
            <div class="cart-item-image">${item.icon}</div>
            <div class="cart-item-details">
                <div class="cart-item-title">${item.name}</div>
                <div class="cart-item-price">$${item.price.toFixed(2)}</div>
            </div>
            <div class="cart-item-quantity">
                <span>${item.quantity}</span>
            </div>
        `;
    cartItems.appendChild(cartItem);
    total += item.price * item.quantity;
  });

  cartTotal.textContent = total.toFixed(2);
}

function checkout() {
  localStorage.setItem("skeletalCart", JSON.stringify(cart));
  window.location.href = "checkout.html";
}

function updateCartCount() {
  const cartCount = document.querySelector(".cart-count");
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  cartCount.textContent = totalItems;
}

// Initialize cart count on page load
updateCartCount();

// Add CSS for animations and error states
const style = document.createElement("style");
style.textContent = `
    .animate-in {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
    
    .form-group input.error,
    .form-group select.error,
    .form-group textarea.error {
        border-color: var(--neon-orange) !important;
        box-shadow: 0 0 20px rgba(255, 102, 0, 0.3) !important;
    }
    
    .info-card:hover .info-icon {
        transform: scale(1.1) rotate(10deg);
        transition: transform 0.3s ease;
    }
    
    .contact-item:hover .contact-icon {
        transform: scale(1.05);
        transition: transform 0.3s ease;
    }
    
    .submit-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
    }
    
    .faq-question:hover {
        color: var(--neon-cyan);
    }
    
    .social-link:hover i {
        color: var(--neon-cyan);
        transform: scale(1.2);
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
